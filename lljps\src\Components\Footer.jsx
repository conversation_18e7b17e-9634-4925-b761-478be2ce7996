import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Typography, Paper, Grid } from '@mui/material';

function Footer() {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubscribe = (e) => {
    e.preventDefault();
    if (email.trim()) {
      setIsSubscribed(true);
      setEmail('');

      setTimeout(() => {
        setIsSubscribed(false);
      }, 3000);
    }
  };

  return (
    <footer className="text-white py-12 bg-brand-secondary">
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

          {/* About Section */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-brand-primary text-white rounded-full flex items-center justify-center text-sm font-bold">
                LLDJPS
              </div>
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 2 }}>
                LINGAYA'S LALITA DEVI JOURNAL OF PROFESSIONAL STUDIES (LLDJPS)
              </Typography>
            </div>
            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                mb: 2,
                lineHeight: 1.8,
              }}
            >
              <strong>ISSN:</strong> 2230-987X <br />
              <strong>Impact Factor:</strong> 4.8 <br />
              <strong>Frequency:</strong>Bi-Annual
            </Typography>

            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                mb: 3,
                lineHeight: 1.6,
              }}
            >
              A <strong>peer-reviewed</strong>, <strong>open access</strong>, CrossRef 
            </Typography>

          </div>

          {/* Quick Links */}
          <Paper
            elevation={2}
            sx={{
              p: 4,
              mb: 4,
              backgroundColor: 'transparent', // ✅ Removes white background
              boxShadow: 'none', // ✅ Removes the shadow effect (optional)
            }}
          >
            <Typography
              variant="h5"
              sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}
            >
              Quick Links
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body1" sx={{ mb: 1 }}>
                  <a href="/submit-article" style={{ color: '#EA7717', textDecoration: 'none' }}>
                    Submit Article
                  </a>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body1" sx={{ mb: 1 }}>
                  <a href="/repository/current" style={{ color: '#EA7717', textDecoration: 'none' }}>
                    Current Issue
                  </a>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body1" sx={{ mb: 1 }}>
                  <a href="/aboutus/instructions" style={{ color: '#EA7717', textDecoration: 'none' }}>
                    Author Guidelines
                  </a>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body1" sx={{ mb: 1 }}>
                  <a href="/policies/publication-policies" style={{ color: '#EA7717', textDecoration: 'none' }}>
                    Publication Policy
                  </a>
                </Typography>
              </Grid>
            </Grid>
          </Paper>


          {/* Contact Information */}
          <div>
            <h3 className="text-lg font-bold mb-4 text-brand-neutral-50">Contact Information</h3>
            <ul className="text-sm space-y-3 text-brand-neutral-200">
              <li className="flex items-center">
                <span className="mr-2">📧</span>
                <a href="mailto:<EMAIL>" className="hover:underline hover:text-brand-neutral-50 transition-colors">
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-center">
                <span className="mr-2">📞</span>
                <a href="tel:+911143791939" className="hover:underline hover:text-brand-neutral-50 transition-colors">
                  011-43791939
                </a>
              </li>

              <li className="flex items-start">
                <span className="mr-2 mt-0.5">🌐</span>
                <a href="https://www.lldjps.com" target="_blank" rel="noopener noreferrer" className="hover:underline hover:text-brand-neutral-50 transition-colors">
                 www.lldjps.com
                </a>
              </li>
              <li className="flex items-center">
                <span className="mr-2">🔗</span>
                <a href="http://dx.doi.org/" className="hover:underline hover:text-brand-neutral-50 transition-colors">
                  Journal DOI
                </a>
              </li>
            </ul>
          </div>

          {/* Newsletter Subscription */}
          <div>
            <h3 className="text-lg font-bold mb-4 text-brand-neutral-50">Stay Updated</h3>
            <p className="text-sm mb-4 text-brand-neutral-200">
              Subscribe to receive notifications about new issues, calls for papers, and journal updates.
            </p>
            {isSubscribed ? (
              <div className="text-white px-4 py-3 rounded-lg text-sm flex items-center bg-brand-primary">
                <span className="mr-2">✓</span>
                Successfully subscribed!
              </div>
            ) : (
              <form onSubmit={handleSubscribe} className="space-y-3">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary text-sm text-brand-text bg-white border border-brand-border"
                  required
                />
                <button
                  type="submit"
                  className="w-full py-3 bg-brand-primary hover:bg-brand-primary-dark text-white rounded-lg text-sm font-semibold transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-brand-primary"
                >
                  Subscribe to Newsletter
                </button>
              </form>
            )}
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-brand-neutral-600">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-brand-neutral-200">
              <p>© 2012-2025 LINGAYA'S LALITA DEVI JOURNAL OF PROFESSIONAL STUDIES (LLDJPS). All rights reserved.</p>
            </div>
            <div className="flex space-x-6 text-sm">
              <Link to="/policies/publication-policies" className="transition-colors text-brand-neutral-200 hover:text-brand-neutral-50">
                Publication Policy
              </Link>
              <Link to="/policies/open-access" className="transition-colors text-brand-neutral-200 hover:text-brand-neutral-50">
                Open Access
              </Link>
              <Link to="/policies/plagiarism" className="transition-colors text-brand-neutral-200 hover:text-brand-neutral-50">
                Plagiarism Policy
              </Link>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="mt-6 pt-6 border-t text-xs text-center border-brand-neutral-600 text-brand-neutral-200">
            <p>
              <strong>Disclaimer:</strong> The views and opinions expressed in the articles published in this journal are those of the individual authors and do not necessarily reflect the official policy or position of LINGAYA'S LALITA DEVI JOURNAL OF PROFESSIONAL STUDIES (LLDJPS), its editorial board, or the publisher. The journal is committed to maintaining the highest standards of academic integrity and ethical publishing practices.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
