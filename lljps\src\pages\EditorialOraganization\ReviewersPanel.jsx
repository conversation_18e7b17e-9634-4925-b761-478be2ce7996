import React from 'react';
import {
  Container,
  <PERSON>po<PERSON>,
  <PERSON>rid,
  <PERSON>,
  CardContent,
  Avatar,
  Box,
  Divider,
  Chip,
  ThemeProvider
} from '@mui/material';
import { styled } from '@mui/material/styles';

import Footer from '../../Components/Footer';
import Navbar from '../../Components/Navbar'; // ✅ Fix: Added Navbar import
import theme from '../../theme/responsiveTheme';

// Styled Components
const StyledCard = styled(Card)(() => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-5px)',
  },
  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
}));

const ReviewersPanel = () => {
  const reviewers = [
    {
      name: 'Dr. <PERSON>',
      expertise: ['Constitutional Law', 'Human Rights'],
      affiliation: 'Harvard Law School',
      image: 'https://via.placeholder.com/120', // 🔁 Replace with actual image URL
      publications: 45,
      yearsOfExperience: 15
    },
    {
      name: 'Prof. <PERSON>',
      expertise: ['International Law', 'Maritime Law'],
      affiliation: 'Yale University',
      image: 'https://via.placeholder.com/120',
      publications: 32,
      yearsOfExperience: 12
    },
    {
      name: 'Dr. Emily Rodriguez',
      expertise: ['Criminal Law', 'Legal Ethics'],
      affiliation: 'Stanford Law School',
      image: 'https://via.placeholder.com/120',
      publications: 28,
      yearsOfExperience: 10
    },
    {
      name: 'Dr. James Wilson',
      expertise: ['Corporate Law', 'Securities Law'],
      affiliation: 'Columbia Law School',
      image: 'https://via.placeholder.com/120',
      publications: 38,
      yearsOfExperience: 14
    },
    {
      name: 'Prof. Lisa Anderson',
      expertise: ['Environmental Law', 'Administrative Law'],
      affiliation: 'UC Berkeley',
      image: 'https://via.placeholder.com/120',
      publications: 42,
      yearsOfExperience: 16
    },
    {
      name: 'Dr. Robert Kumar',
      expertise: ['Intellectual Property', 'Technology Law'],
      affiliation: 'MIT Law',
      image: 'https://via.placeholder.com/120',
      publications: 35,
      yearsOfExperience: 11
    }
  ];

  return (
    <>
      <div style={{ backgroundColor: 'var(--brand-surface)', minHeight: '100vh' }}>
        <ThemeProvider theme={theme}>
          <Container maxWidth="lg" sx={{ py: 8 }}>
            {/* Page Title */}
            <Typography
              variant="h2"
              align="center"
              gutterBottom
              sx={{ color: 'primary.main', fontWeight: 'bold', mb: 6 }}
            >
              Reviewers Panel
            </Typography>

            {/* Subtitle */}
            <Typography
              variant="h6"
              align="center"
              sx={{
                mb: 8,
                color: 'text.secondary',
                maxWidth: '900px',
                mx: 'auto',
                lineHeight: 1.6
              }}
            >
              Our distinguished panel of expert reviewers ensures the highest standards of academic excellence through rigorous peer review processes.
            </Typography>

            {/* Statistics */}
            <Box sx={{ mb: 8 }}>
              <Grid container spacing={4} justifyContent="center">
                <Grid item xs={12} sm={4} textAlign="center">
                  <Typography variant="h3" color="primary.main" fontWeight="bold">
                    50+
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Expert Reviewers
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4} textAlign="center">
                  <Typography variant="h3" color="primary.main" fontWeight="bold">
                    15+
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Years Average Experience
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4} textAlign="center">
                  <Typography variant="h3" color="primary.main" fontWeight="bold">
                    1000+
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Papers Reviewed
                  </Typography>
                </Grid>
              </Grid>
            </Box>

            {/* Reviewers Grid */}
            <Grid container spacing={4} justifyContent="center" alignItems="stretch">
              {reviewers.map((reviewer, index) => (
                <Grid item xs={12} sm={6} md={4} key={index} sx={{ display: 'flex' }}>
                  <StyledCard sx={{ width: '100%' }}>
                    <CardContent
                      sx={{
                        textAlign: 'center',
                        p: 3,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        height: '100%',
                        minHeight: '450px'
                      }}
                    >
                      {/* Avatar and Name */}
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
                        <Avatar
                          src={reviewer.image}
                          alt={reviewer.name}
                          sx={{
                            width: 120,
                            height: 120,
                            mb: 2,
                            border: '4px solid',
                            borderColor: 'primary.main',
                            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                          }}
                        />
                        <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                          {reviewer.name}
                        </Typography>
                        <Typography variant="subtitle1" color="primary.main" sx={{ mb: 2 }}>
                          {reviewer.affiliation}
                        </Typography>
                      </Box>

                      <Divider sx={{ my: 2, width: '100%' }} />

                      {/* Expertise */}
                      <Box sx={{ mb: 2, flex: 1 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 'medium' }}>
                          Areas of Expertise
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: 0.5 }}>
                          {reviewer.expertise.map((area, i) => (
                            <Chip
                              key={i}
                              label={area}
                              size="small"
                              color="primary"
                              variant="outlined"
                              sx={{ fontSize: '0.75rem', height: '24px' }}
                            />
                          ))}
                        </Box>
                      </Box>

                      {/* Bottom Stats */}
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-around',
                          mt: 'auto',
                          pt: 2,
                          width: '100%',
                          borderTop: '1px solid',
                          borderColor: 'divider'
                        }}
                      >
                        <Box textAlign="center">
                          <Typography variant="h6" color="primary.main" fontWeight="bold">
                            {reviewer.publications}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Publications
                          </Typography>
                        </Box>
                        <Box textAlign="center">
                          <Typography variant="h6" color="primary.main" fontWeight="bold">
                            {reviewer.yearsOfExperience}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Years Experience
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </StyledCard>
                </Grid>
              ))}
            </Grid>
          </Container>
        </ThemeProvider>
      </div>
      <Footer />
    </>
  );
};

export default ReviewersPanel;
