import React, { useState } from 'react';
import { Outlet, Link } from 'react-router-dom';
import Navbar from './Navbar';
import Footer from './Footer';

// Sidebar Link Component
const SidebarLink = ({ label, indent, href = "#" }) => {
  const displayLabel = typeof label === 'string' ? label : String(label);
  const isInternal = href && href.startsWith('/');

  return (
    <li className={indent ? 'pl-4' : ''}>
      {isInternal ? (
        <Link
          to={href}
          className="text-brand-text hover:text-brand-primary transition-colors duration-200 text-sm block py-1 text-left"
        >
          {displayLabel}
        </Link>
      ) : (
        <a
          href={href || '#'}
          target="_blank"
          rel="noopener noreferrer"
          className="text-brand-text hover:text-brand-primary transition-colors duration-200 text-sm block py-1 text-left"
        >
          {displayLabel}
        </a>
      )}
    </li>
  );
};

// Dropdown Section for Mobile
const DropdownSection = ({ title, items }) => {
  const [open, setOpen] = useState(false);

  return (
    <div className="mb-6 text-left">
      <button
        onClick={() => setOpen(!open)}
        className="w-full text-left text-base font-semibold text-brand-text mb-2 border-b border-brand-border pb-1 uppercase tracking-wide flex justify-between items-center"
        aria-expanded={open}
      >
        {title}
        <span className="text-sm">{open ? '▲' : '▼'}</span>
      </button>
      {open && (
        <ul className="space-y-2">
          {items.map((item, index) => (
            <SidebarLink
              key={`${title}-${index}`}
              label={item.label}
              href={item.href}
              indent
            />
          ))}
        </ul>
      )}
    </div>
  );
};

// 🔔 Notification Bar Component
const NotificationBar = () => (
  <div className="bg-brand-primary text-white rounded-lg shadow p-3 mb-6">
    <h3 className="font-bold text-lg mb-2">CALL FOR PAPERS</h3>
    <div className="bg-white text-brand-primary rounded p-2 shadow">
      <div className="font-semibold text-base mb-1">
        <span className="text-red-600 font-bold text-xs align-top ml-1">NEW</span>
      </div>
      <div className="text-sm">
        <div><b>Paper Submission:</b> </div>
        <div><b>Author Notification:</b> </div>
        <div><b>Journal Publication:</b> </div>
      </div>
    </div>
  </div>
);

const Layout = () => {
  const subjectAreas = [
    'Arts and Humanities',
    'Business and Management',
    'Engineering and Technology',
    'Economics and Public Policy',
    'Legal Studies',
    'Linguistic Studies',
    'Mass Communication',
    'Public Health',
  ];

  return (
    <>
      <Navbar />
      <div className="min-h-screen grid grid-cols-1 md:grid-cols-[300px_1fr] bg-brand-surface">
        {/* Sidebar */}
        <aside className="bg-white p-4 sm:p-6 border-r border-brand-border shadow-md overflow-y-auto text-sm h-auto md:h-screen sticky top-0 z-20 w-full md:w-[300px] transition-all duration-300">
           <Link
                to="/submit-article"
                className="block w-full text-left mb-5 bg-brand-primary hover:bg-brand-primary-dark text-white font-semibold py-2 px-4 rounded shadow transition duration-200 uppercase text-sm min-h-[44px]"
              >
                Submit Article
              </Link>
          <NotificationBar /> {/* Add this line at the top of your sidebar */}
          {/* Desktop View */}
          <div className="hidden md:block">
            <div className="mb-6">
           
            </div>

            {/* Navigation Sections */}
            <Section title="Subject Areas" items={subjectAreas} />
          </div>
        </aside>

        {/* Main Content */}
        <main className="bg-white p-4 sm:p-6 md:p-8 overflow-y-auto shadow-inner min-h-[60vh] rounded-tl-2xl md:rounded-none">
          <Outlet />
        </main>
      </div>
      <Footer />
    </>
  );
};

// Helper Section Component for Desktop
const Section = ({ title, items }) => (
  <div className="mb-6 text-left">
    <h2 className="text-base font-semibold text-brand-text mb-2 border-b border-brand-border pb-1 uppercase tracking-wide text-left">
      {title}
    </h2>
    <ul className="space-y-2">
      {items.map((item, index) => {
        const label = typeof item === 'string' ? item : item.label;
        const href = typeof item === 'string' ? '#' : item.href;
        return <SidebarLink key={`${title}-${index}`} label={label} href={href} indent />;
      })}
    </ul>
  </div>
);

export default Layout;
