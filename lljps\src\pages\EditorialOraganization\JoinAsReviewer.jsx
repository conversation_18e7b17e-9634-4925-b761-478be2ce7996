import { useState } from 'react';
import {
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Box,
  Alert,
  Chip,
  Paper,
  ThemeProvider,
  createTheme
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';

// Theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#EA7717',
      light: '#ff8a4c',
      dark: '#b91c1c',
    },
    secondary: {
      main: '#feecdc',
    },
    background: {
      default: 'var(--brand-surface)',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: { fontWeight: 700 },
    h2: { fontWeight: 700 },
    h3: { fontWeight: 600 },
    h4: { fontWeight: 600 },
  },
});

// Styled Components
const StyledCard = styled(Card)(({ theme }) => ({
  boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
  borderRadius: '16px',
  border: `1px solid ${theme.palette.primary.light}20`,
  backgroundColor: 'var(--brand-white)',
  height: '100%',
  transition: 'all 0.3s ease',
}));

const StyledButton = styled(Button)(({ theme }) => ({
  background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.light} 90%)`,
  borderRadius: '12px',
  padding: '12px 32px',
  fontSize: '1rem',
  fontWeight: 600,
  textTransform: 'none',
  boxShadow: '0 4px 16px rgba(234, 88, 12, 0.2)',
  '&:hover': {
    background: `linear-gradient(45deg, ${theme.palette.primary.dark} 30%, ${theme.palette.primary.main} 90%)`,
    boxShadow: '0 6px 20px rgba(234, 88, 12, 0.3)',
    transform: 'translateY(-1px)',
  },
  '&:disabled': {
    background: 'grey',
    boxShadow: 'none',
  },
  transition: 'all 0.3s ease',
  [theme.breakpoints.down('sm')]: {
    width: '100%',
    padding: '10px 24px',
  },
}));

const BenefitCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  textAlign: 'center',
  borderRadius: '16px',
  border: `1px solid ${theme.palette.primary.light}20`,
  backgroundColor: 'var(--brand-surface)',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 24px rgba(0,0,0,0.1)',
  },
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '12px',
    backgroundColor: '#ffffff',
    transition: 'all 0.3s ease',
    '&:hover': {
      backgroundColor: '#f8fafc',
    },
    '&.Mui-focused': {
      backgroundColor: '#ffffff',
    },
  },
  '& .MuiInputLabel-root': {
    fontWeight: 500,
    color: theme.palette.text.secondary,
  },
  '& .MuiFormHelperText-root': {
    marginLeft: 0,
  },
}));

// Component
const JoinAsReviewer = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    expertise: '',
    profileLink: '',
    message: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState({});

  const benefits = [
    { title: 'Contribute to Scholarship', description: 'Shape the future of legal research...', icon: '📚' },
    { title: 'Build Professional Network', description: 'Connect with leading academics...', icon: '🤝' },
    { title: 'Enhance Credibility', description: 'Get recognized as an expert reviewer...', icon: '⭐' },
    { title: 'Stay Current', description: 'Access the latest research before publication...', icon: '🔍' },
    { title: 'Professional Recognition', description: 'Receive certificates and acknowledgment...', icon: '🏆' },
    { title: 'Flexible Commitment', description: 'Review papers on your schedule...', icon: '⏰' }
  ];

  const expertiseAreas = [
    'Constitutional Law', 'Criminal Law', 'Corporate Law', 'International Law',
    'Human Rights Law', 'Environmental Law', 'Intellectual Property', 'Administrative Law',
    'Family Law', 'Tax Law', 'Labor Law', 'Maritime Law'
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) newErrors.name = 'Full name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    else if (!/^\S+@\S+\.\S+$/.test(formData.email)) newErrors.email = 'Enter a valid email';
    if (!formData.expertise.trim()) newErrors.expertise = 'Area of expertise is required';
    if (!formData.message.trim()) newErrors.message = 'Background info is required';
    else if (formData.message.trim().length < 50) newErrors.message = 'Min 50 characters required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      // TODO: Implement actual form submission to backend
      setIsSubmitted(true);
      setTimeout(() => {
        setFormData({ name: '', email: '', expertise: '', profileLink: '', message: '' });
        setIsSubmitted(false);
      }, 5000);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', bgcolor: 'background.default' }}>
        <Box sx={{ background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`, color: 'white', py: 8, textAlign: 'center' }}>
          <Container maxWidth="lg">
            <Typography variant="h2" sx={{ fontWeight: 700, mb: 2 }}>Join Our Expert Review Panel</Typography>
            <Typography variant="h6" sx={{ maxWidth: '800px', mx: 'auto', opacity: 0.9 }}>
              Become part of an elite community shaping the future of legal scholarship
            </Typography>
          </Container>
        </Box>

        <Container maxWidth="lg" sx={{ py: 6 }}>
          <Box sx={{ mb: 8 }}>
            <Typography variant="h3" align="center" sx={{ color: 'primary.main', fontWeight: 600, mb: 6 }}>
              Why Become a Reviewer?
            </Typography>
            <Grid container spacing={3}>
              {benefits.map((benefit, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <BenefitCard>
                    <Typography variant="h3" sx={{ fontSize: '2.5rem' }}>{benefit.icon}</Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main', mt: 2 }}>
                      {benefit.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1.5 }}>
                      {benefit.description}
                    </Typography>
                  </BenefitCard>
                </Grid>
              ))}
            </Grid>
          </Box>

          <Box sx={{ mb: 8 }}>
            <Grid container justifyContent="center">
              <Grid item xs={12} sm={10} md={8}>
                <StyledCard>
                  <CardContent sx={{ p: 4 }}>
                    <Typography variant="h4" align="center" sx={{ color: 'primary.main', fontWeight: 600, mb: 4 }}>
                      Application Form
                    </Typography>

                    {isSubmitted && (
                      <Alert severity="success" sx={{ mb: 3 }}>
                        Application received! We'll contact you within 5–7 business days.
                      </Alert>
                    )}

                    <Box component="form" onSubmit={handleSubmit} noValidate>
                      <Grid container spacing={3}>
                        <Grid item xs={12} sm={6}>
                          <StyledTextField fullWidth label="Full Name *" name="name" value={formData.name} onChange={handleInputChange} error={!!errors.name} helperText={errors.name} />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <StyledTextField fullWidth label="Email Address *" name="email" value={formData.email} onChange={handleInputChange} error={!!errors.email} helperText={errors.email} />
                        </Grid>
                        <Grid item xs={12}>
                          <StyledTextField fullWidth label="Primary Area of Expertise *" name="expertise" value={formData.expertise} onChange={handleInputChange} error={!!errors.expertise} helperText={errors.expertise || "e.g., Constitutional Law"} />
                          <Box sx={{ mt: 2 }}>
                            <Typography sx={{ mb: 1.5, fontWeight: 500, fontSize: '0.9rem' }}>
                              Suggested expertise areas:
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                              {expertiseAreas.map((area) => (
                                <Chip
                                  key={area}
                                  label={area}
                                  onClick={() => setFormData(prev => ({ ...prev, expertise: area }))}
                                  sx={{
                                    cursor: 'pointer',
                                    borderRadius: '16px',
                                    '&:hover': {
                                      bgcolor: 'primary.main',
                                      color: 'white',
                                    },
                                  }}
                                />
                              ))}
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12}>
                          <StyledTextField fullWidth label="Professional Profile URL" name="profileLink" value={formData.profileLink} onChange={handleInputChange} helperText="Optional: LinkedIn or academic profile" />
                        </Grid>
                        <Grid item xs={12}>
                          <StyledTextField fullWidth label="About Yourself *" name="message" value={formData.message} onChange={handleInputChange} error={!!errors.message} helperText={errors.message || "Describe your background and interest (min. 50 characters)"} multiline rows={4} />
                        </Grid>
                        <Grid item xs={12} sx={{ textAlign: 'center', mt: 3 }}>
                          <StyledButton type="submit" variant="contained" disabled={isSubmitted}>
                            {isSubmitted ? 'Submitted' : 'Apply Now'}
                          </StyledButton>
                        </Grid>
                      </Grid>
                    </Box>
                  </CardContent>
                </StyledCard>
              </Grid>
            </Grid>
          </Box>

          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography variant="h4" sx={{ color: 'primary.main', fontWeight: 600, mb: 4 }}>
              Application Process
            </Typography>
            <Grid container spacing={3}>
              {[
                { title: 'Application Review', desc: 'We review your application within 5–7 business days.' },
                { title: 'Interview', desc: 'Selected candidates will be interviewed.' },
                { title: 'Onboarding', desc: 'Successful applicants receive orientation and assignments.' },
              ].map((step, i) => (
                <Grid item xs={12} sm={4} key={i}>
                  <Box sx={{ p: 3, borderRadius: '12px', bgcolor: 'rgba(234,88,12,0.05)' }}>
                    <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 600, mb: 1.5 }}>
                      {i + 1}. {step.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {step.desc}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Container>
        <Footer />
      </Box>
    </ThemeProvider>
  );
};

export default JoinAsReviewer;
