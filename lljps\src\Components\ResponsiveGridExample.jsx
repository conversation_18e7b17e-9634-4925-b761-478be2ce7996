import { Grid, Card, CardContent, Typography, Box } from '@mui/material';

function ResponsiveGridExample({ items }) {
  return (
    <Grid container spacing={{ xs: 2, md: 3 }}>
      {items.map((item, index) => (
        <Grid item xs={12} sm={6} md={4} key={index}>
          <Card 
            sx={{ 
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              transition: 'transform 0.3s ease',
              '&:hover': {
                transform: 'translateY(-5px)'
              }
            }}
          >
            <CardContent>
              <Typography variant="h5" component="h2" gutterBottom>
                {item.title}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {item.description}
              </Typography>
            </CardContent>
            <Box sx={{ flexGrow: 1 }} />
            <Box sx={{ p: 2 }}>
              {item.footer}
            </Box>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
}

export default ResponsiveGridExample;