import React from 'react';
import Footer from '../../Components/Footer';
import { Container, Typography, Paper, List, ListItem, ListItemIcon, ListItemText, Box, ThemeProvider } from '@mui/material';
import { Check as CheckIcon, Block as BlockIcon } from '@mui/icons-material';
import theme from '../../theme/responsiveTheme';

function Plagiarism() {
    return (
        <>
            <ThemeProvider theme={theme}>
                <div className="min-h-screen bg-brand-orange-50">
                    <Container maxWidth="lg" sx={{ py: 8 }}>
                <Typography variant="h2"
                    component="h1" align="center"
                    gutterBottom sx={{
                        color: 'primary.main', fontWeight: 'bold',
                        mb: 6,
                    }}
                >
                    Plagiarism Policy                    </Typography>
                <Paper elevation={3} sx={{ p: 4, mb: 4 }}>                        <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">
                    Zero Tolerance for Plagiarism                        </Typography>
                    <Typography paragraph>
                        The Journal of Legal Studies (JLS) maintains a strict zero-tolerance policy towards plagiarism. All submissions undergo rigorous plagiarism checks using advanced detection software.                        </Typography>
                    <List>                            <ListItem>
                        <ListItemIcon>                                    <CheckIcon color="primary" />
                        </ListItemIcon>                                <ListItemText
                            primary="Plagiarism Detection" secondary="All manuscripts are screened through multiple plagiarism detection tools"
                        />                            </ListItem>
                        <ListItem>                                <ListItemIcon>
                            <BlockIcon color="error" />                                </ListItemIcon>
                            <ListItemText primary="Consequences"
                                secondary="Detected plagiarism results in immediate rejection and potential ban from future submissions" />
                        </ListItem>
                    </List>                    </Paper>
                <Paper elevation={3} sx={{ p: 4, mb: 4 }}>                        <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">
                    Types of Plagiarism                        </Typography>
                    <List>                            <ListItem>
                        <ListItemIcon>                                    <BlockIcon color="error" />
                        </ListItemIcon>                                <ListItemText
                            primary="Direct Plagiarism" secondary="Word-for-word copying without proper citation"
                        />                            </ListItem>
                        <ListItem>                                <ListItemIcon>
                            <BlockIcon color="error" />                                </ListItemIcon>
                            <ListItemText primary="Self-Plagiarism"
                                secondary="Reusing one's own previously published work without acknowledgment" />
                        </ListItem>                            <ListItem>
                            <ListItemIcon>                                    <BlockIcon color="error" />
                            </ListItemIcon>                                <ListItemText
                                primary="Mosaic Plagiarism" secondary="Borrowing phrases from a source without using quotation marks"
                            />                            </ListItem>
                    </List>
                    </Paper>
                    </Container>
                </div>
            </ThemeProvider>
            <Footer />
        </>
    )
}

export default Plagiarism;
