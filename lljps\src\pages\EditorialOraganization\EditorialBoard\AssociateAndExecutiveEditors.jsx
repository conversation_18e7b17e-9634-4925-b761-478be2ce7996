import React from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  Avatar,
  Box,
  Chip,
  CardContent
} from '@mui/material';
import Footer from '../../../Components/Footer';
import image1 from '../../../assets/Images/jyoti.jpg'
import image2 from '../../../assets/Images/saleem.jpg'
import image3 from '../../../assets/Images/rajesh sir (2).jpg'


// Placeholder Image
const placeholderImage = 'https://via.placeholder.com/140x140.png?text=No+Image';

// Data for Associate Editors and Executive Editor
const editorialData = {
  associateEditors: [
    {
      name: 'Dr <PERSON><PERSON><PERSON>',
      position: 'HoD, Department of Education',
      institution: "Lingaya's Lalita Devi Institute of Management & Sciences, Delhi, India",
      bio: 'Advanced credentials including Psychology, Political Science, Guidance & Counselling, with multidisciplinary expertise.',
      image: image1,
      specialization: ['Psychology', 'Political Science', 'Guidance & Counselling'],
    },
    {
      name: 'Dr. <PERSON><PERSON>',
      position: 'Ho<PERSON>, Department of Journalism',
      institution: "Lingaya's <PERSON><PERSON> Institute of Management & Sciences, Delhi, India",
      bio: 'An accomplished scholar in film studies, with pedagogical expertise in concept-to-production learning, inquiry-driven methods, flipped classrooms, and collaborative projects.',
      image: image2, // Add image URL if available
      specialization: ['Film Studies', 'Pedagogy', 'Collaborative Learning'],
    },
  ],
  executiveEditor: {
    name: 'Rajesh R. Mishra',
    position: 'Executive Editor',
    institution: 'Responsible for day-to-day affairs of the Journal',
    bio: 'Expertise in Agile methodology, Research Analysis, and Data Science.',
    image: image3, // Add image URL if available
    specialization: ['Agile Methodology', 'Research Analysis', 'Data Science'],
  },
};

const cardStyles = {
  display: 'flex',
  flexDirection: { xs: 'column', md: 'row' },
  alignItems: { xs: 'center', md: 'flex-start' },
  p: 3,
  gap: 3,
  borderRadius: 3,
  minHeight: 220, // ✅ Same height for consistency
};

const AssociateAndExecutiveEditors = () => {
  return (
    <>
      <Box sx={{ py: 6, backgroundColor: '#f9f9f9' }}>
        <Container maxWidth="lg">
          {/* Associate Editors */}
          <Typography
            variant="h3"
            textAlign="center"
            color="primary"
            fontWeight="bold"
            mb={4}
          >
            Associate Editors
          </Typography>
          <Grid container spacing={4}>
            {editorialData.associateEditors.map((editor, index) => (
              <Grid item xs={12} key={index}>
                <Card elevation={3} sx={cardStyles}>
                  {/* Left: Image */}
                  <Box
                    sx={{
                      width: 140,
                      height: 140,
                      border: '2px solid',
                      borderColor: 'primary.main',
                      borderRadius: 2,
                      overflow: 'hidden',
                      flexShrink: 0,
                    }}
                  >
                    <Avatar
                      variant="square"
                      src={editor.image }
                      alt={editor.name}
                      sx={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                      }}
                    />
                  </Box>

                  {/* Right: Details */}
                  <CardContent sx={{ p: 0, flex: 1 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      {editor.name}
                    </Typography>
                    <Typography variant="body1" fontWeight="bold" color="text.primary">
                      {editor.position}
                    </Typography>
                    <Typography
                    fontWeight="bold"
                      display="block"
                      color="text.secondary"
                      mb={1}
                    >
                      {editor.institution}
                    </Typography>
                    {editor.bio && (
                      <Typography  sx={{ mb: 2 }}>
                        {editor.bio}
                      </Typography>
                    )}
                    <Box display="flex" flexWrap="wrap" gap={1}>
                      {editor.specialization.map((spec, idx) => (
                        <Chip
                          key={idx}
                          label={spec}
                          size="small"
                          variant="outlined"
                          color="primary"
                        />
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Executive Editor */}
          <Typography
            variant="h3"
            textAlign="center"
            color="primary"
            fontWeight="bold"
            my={6}
          >
            Executive Editor
          </Typography>
          <Grid container justifyContent="center">
            <Grid item xs={12}>
              <Card elevation={3} sx={cardStyles}>
                {/* Left: Image */}
                <Box
                  sx={{
                    width: 140,
                    height: 140,
                    border: '2px solid',
                    borderColor: 'primary.main',
                    borderRadius: 2,
                    overflow: 'hidden',
                    flexShrink: 0,
                  }}
                >
                  <Avatar
                    variant="square"
                    src={editorialData.executiveEditor.image || placeholderImage}
                    alt={editorialData.executiveEditor.name}
                    sx={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  />
                </Box>

                {/* Right: Details */}
                <CardContent sx={{ p: 0, flex: 1 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    {editorialData.executiveEditor.name}
                  </Typography>
                  <Typography variant="body1" fontWeight="bold" color="text.primary">
                    {editorialData.executiveEditor.position}
                  </Typography>
                  <Typography
                    variant="caption"
                    display="block"
                    color="text.secondary"
                    mb={1}
                  >
                    {editorialData.executiveEditor.institution}
                  </Typography>
                  {editorialData.executiveEditor.bio && (
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      {editorialData.executiveEditor.bio}
                    </Typography>
                  )}
                  <Box display="flex" flexWrap="wrap" gap={1}>
                    {editorialData.executiveEditor.specialization.map((spec, idx) => (
                      <Chip
                        key={idx}
                        label={spec}
                        size="small"
                        variant="outlined"
                        color="primary"
                      />
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </>
  );
};

export default AssociateAndExecutiveEditors;
