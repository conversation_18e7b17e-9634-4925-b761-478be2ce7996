import React from 'react';
import Footer from '../../Components/Footer';
import Navbar from '../../Components/Navbar';
import { Container, Typography, Paper, Link } from '@mui/material';

function CopyrightPolicies() {
  return (
    <>
      <div className="min-h-screen bg-orange-50">
        <Container maxWidth="lg" sx={{ py: 8 }}>
          {/* Main Heading */}
          <Typography
            variant="h2"
            component="h1"
            align="center"
            gutterBottom
            sx={{ color: 'primary.main', fontWeight: 'bold', mb: 6 }}
          >
            Copyright & Archiving Policies
          </Typography>

          {/* Copyright Policy */}
          <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
            <Typography variant="h5" gutterBottom color="primary">
              Copyright Policy
            </Typography>
          <Typography paragraph>
  All articles in <strong>LLDJPS</strong> are published under the CC BY-NC-SA 4.0 
  (Creative Commons Attribution-NonCommercial-ShareAlike 4.0 International). 
  Upon acceptance of your article, you will be required to complete the CC BY License 
  through Creative Commons. This license requires that reusers give credit to the creator. 
  It allows reusers to distribute, remix, adapt, and build upon the material in any medium 
  or format, for noncommercial purposes only. If others modify or adapt the material, 
  they must license the modified material under identical terms.
</Typography>

<Typography paragraph>
  Under this license terms, anyone can copy and redistribute the material in any medium 
  or format. They can adapt the material in any format including remix, transform, and 
  build upon the material. Although, these freedoms cannot be revoked as long as users 
  follow the terms below:
</Typography>

<ul className="list-disc pl-6 space-y-1 text-gray-700">
  <li>
    <strong>Attribution:</strong> You must give appropriate credit, provide a link to 
    the license, and indicate if changes were made. You may do so in any reasonable manner, 
    but not in any way that suggests the licensor endorses you or your use.
  </li>
  <li>
    <strong>Non-Commercial Use:</strong> You may not use the material for commercial purposes.
  </li>
  <li>
    <strong>Share Alike:</strong> If you remix, transform, or build upon the material, 
    you must distribute your contributions under the same license as the original.
  </li>
</ul>

<Typography paragraph>
  Users cannot apply legal terms or technological measures that legally restrict others 
  from doing anything the license permits. The license does not apply to material in the 
  public domain or uses covered by legal exceptions. No warranties are provided. Other rights 
  (such as privacy, publicity, or moral rights) may still apply.
</Typography>

            <Typography component="div" paragraph>
              Key terms of this license:
              <ul className="list-disc pl-6 mt-2 space-y-1">
                <li>
                  <strong>Attribution:</strong> Credit the creator, provide a license link, and
                  indicate changes (without implying endorsement).
                </li>
                <li>
                  <strong>Non-Commercial:</strong> The work cannot be used for commercial purposes.
                </li>
                <li>
                  <strong>Share Alike:</strong> Derivatives must be licensed under the same terms.
                </li>
              </ul>
            </Typography>
            <Typography paragraph>
              For complete details, visit:{' '}
              <Link
                href="https://creativecommons.org/licenses/by-nc-sa/4.0/"
                target="_blank"
                rel="noopener noreferrer"
                color="secondary"
              >
              https://creativecommons.org/licenses/by-nc-sa/4.0/
              </Link>
            </Typography>
          </Paper>

          {/* Plagiarism Policy */}
          <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
            <Typography variant="h5" gutterBottom color="primary">
              Plagiarism Checking Policy
            </Typography>
            <Typography paragraph>
              <strong>LLDJPS</strong> treats issues such as copyright infringement, plagiarism, and
              breaches of publication ethics with utmost seriousness. All submissions are screened
              using duplication-detection tools.
            </Typography>
            <Typography paragraph>
              In cases of plagiarism or unauthorized use, the journal may take actions such as:
            </Typography>
            <ul className="list-disc pl-6 mb-3 space-y-1 text-gray-700">
              <li>Issuing an erratum or corrigendum</li>
              <li>Retracting the article</li>
              <li>Notifying institutional or academic authorities</li>
              <li>Pursuing legal remedies if required</li>
            </ul>
          </Paper>

          {/* Archiving Policy */}
          <Paper elevation={3} sx={{ p: 4 }}>
            <Typography variant="h5" gutterBottom color="primary">
              Archiving Policy
            </Typography>

            {/* Sections inside Archiving */}
            {[
              {
                title: 'Purpose and Scope',
                text: `Defines the framework for preserving, accessing, and disseminating all published content.
                Ensures permanent availability of research articles for academic use and maintains long-term
                integrity of the journal.`,
              },
              {
                title: 'Archiving Methods',
                text: `The journal employs multiple methods such as: 
                  • Secure digital archives with backups 
                  • Collaborations with institutional repositories`,
              },
              {
                title: 'Archiving Responsibility',
                text: `The Editorial Board oversees archiving, ensuring alignment with best practices and standards.`,
              },
              {
                title: 'Metadata Preservation',
                text: `Essential metadata (author, date, keywords, abstracts) is preserved for indexing, discoverability, and citation.`,
              },
              {
                title: 'Accessibility and Availability',
                text: `Archived content is accessible via the journal website and repositories, ensuring ongoing availability.`,
              },
              {
                title: 'Digital Preservation Standards',
                text: `Adheres to durable file formats, integrity checks, and metadata management to maintain authenticity.`,
              },
              {
                title: 'Permanent URLs',
                text: `Each article is assigned a permanent URL for reliable long-term access.`,
              },
              {
                title: 'Backup and Redundancy',
                text: `Regular backups to multiple secure locations protect against data loss.`,
              },
              {
                title: 'Updates and Migration',
                text: `Content is migrated to newer formats and platforms when needed to maintain accessibility.`,
              },
              {
                title: 'Compliance with Standards',
                text: `Complies with international standards and best practices for archiving scholarly content.`,
              },
              {
                title: 'Continuous Improvement',
                text: `The Archiving Policy is periodically reviewed and updated in line with evolving technologies.`,
              },
            ].map((section, index) => (
              <article
                key={index}
                className="bg-white p-6 rounded-2xl shadow-sm mb-4 last:mb-0"
              >
                <h3 className="text-xl font-bold mb-3">{section.title}</h3>
                <p className="text-gray-700 leading-relaxed">{section.text}</p>
              </article>
            ))}
          </Paper>
        </Container>
      </div>
    </>
  );
}

export default CopyrightPolicies;
