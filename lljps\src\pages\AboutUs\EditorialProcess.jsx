import React from 'react';
import { Typography, Box, Paper } from '@mui/material';

const EditorialProcess = () => {
  return (
    <Box sx={{ maxWidth: 900, mx: 'auto', p: 3 }}>
      <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 4, textAlign: 'center' }}>
        Editorial Process
      </Typography>
      <Paper elevation={2} sx={{ p: 4 }}>
        <Typography variant="body1">
          The Journal of Content, Community & Communication (JCCC) follows a rigorous editorial process to ensure the quality and integrity of published research. All submissions undergo initial screening, peer review, and final editorial decision. The process is designed to maintain high academic standards and ethical publishing practices.
        </Typography>
      </Paper>
    </Box>
  );
};

export default EditorialProcess;