{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "@eslint/js/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "react-hooks"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "error", "no-var": "error", "object-shorthand": "error", "prefer-template": "error", "template-curly-spacing": "error", "arrow-spacing": "error", "comma-dangle": ["error", "never"], "quotes": ["error", "single", {"avoidEscape": true}], "semi": ["error", "always"], "indent": ["error", 2], "no-trailing-spaces": "error", "eol-last": "error", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}, "settings": {"react": {"version": "detect"}}}