import React, { useState } from 'react';
import { Container, Typo<PERSON>, <PERSON>, Card, Card<PERSON>ontent, But<PERSON> } from '@mui/material';
import { PictureAsPdf, Download, Visibility } from '@mui/icons-material';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';
import currentIssuePdf from '../../assets/Current Isssue/Volume-12, Issue-1 2025.pdf';

const CurrentIssuse = () => {
  const [searchTerm, setSearchTerm] = useState('');

  // Articles data from IJAR website
  const articles = [
    {
      title: "SUSTAINABLE COCOA MANAGEMENT AND PRODUCTION BASED ON LOCAL KNOWLEDGE IN CENTRAL COTE D'IVOIRE",
      authors: "<PERSON><PERSON><PERSON> Venance <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>",
      abstract: "This research examines sustainable cocoa management practices in Central Côte d'Ivoire, focusing on local knowledge systems and their integration with modern agricultural practices.",
      doi: "/21058",
      keywords: ["Cocoa", "Sustainability", "Local Knowledge", "Côte d'Ivoire"],
      downloads: 179,
      views: 0,
      category: "Agriculture and Environment"
    },
    {
      title: "THE IMPACT OF SUBSCRIPTION-BASED MODELS ON CONSUMER BEHAVIOR: A COMPARATIVE STUDY OF NETFLIX AND SPOTIFY",
      authors: "Gajendra G. S. and Rashmi She<PERSON>",
      abstract: "This study analyzes the impact of subscription-based business models on consumer behavior, comparing the strategies and outcomes of Netflix and Spotify in the digital entertainment industry.",
      doi: "/21060",
      keywords: ["Subscription Models", "Consumer Behavior", "Netflix", "Spotify"],
      downloads: 25,
      views: 0,
      category: "Business and Management"
    },
    {
      title: "FETO MATERNAL OUTCOME IN POSTDATED PREGNANCY IN A TERTIARY CARE CENTRE",
      authors: "Kavita Kuldeep, Vineeta Garg, Pravin Sasane and Nitin Tiwari",
      abstract: "This research investigates fetal and maternal outcomes in postdated pregnancies, providing insights into management strategies and risk factors in a tertiary care setting.",
      doi: "/21059",
      keywords: ["Postdated Pregnancy", "Maternal Outcome", "Fetal Outcome", "Tertiary Care"],
      downloads: 21,
      views: 0,
      category: "Health and Medicine"
    },
    {
      title: "COMPARATIVE STUDY ON THE EFFECTS OF EXTRUDED AND NON-EXTRUDED LOCAL FEEDS VERSUS COMMERCIAL FEED ON THE ZOOTECHNICAL PERFORMANCE OF OREOCHROMIS NILOTICUS (LINNAEUS, 1758)",
      authors: "Jean Fall, Coumba Diao, Paul Mamadou Ndour, Alla Ndione and Ndeye Codou Mbaye",
      abstract: "This comparative study evaluates the effects of different feed types on the zootechnical performance of Oreochromis niloticus, providing insights into sustainable aquaculture practices.",
      doi: "/21063",
      keywords: ["Aquaculture", "Feed", "Oreochromis niloticus", "Zootechnical Performance"],
      downloads: 19,
      views: 0,
      category: "Agriculture and Environment"
    },
    {
      title: "CLINICAL UTILITY OF SHOCK INDEX IN THE EARLY DETECTION OF ADVERSE OUTCOMES IN POSTPARTUM HEMORRHAGE",
      authors: "Anchala Mahilange, M.S.Darshana and Supriya Gupta",
      abstract: "This study examines the clinical utility of shock index as an early predictor of adverse outcomes in postpartum hemorrhage, contributing to improved maternal care protocols.",
      doi: "/21065",
      keywords: ["Shock Index", "Postpartum Hemorrhage", "Maternal Care", "Clinical Utility"],
      downloads: 11,
      views: 0,
      category: "Health and Medicine"
    }
  ];

  const filteredArticles = articles.filter(article =>
    article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    article.authors.toLowerCase().includes(searchTerm.toLowerCase()) ||
    article.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography
          variant="h3"
          component="h1"
          sx={{
            fontWeight: 700,
            color: '#EA7717',
            mb: 4,
            textAlign: 'left'
          }}
        >
          Current Issue
        </Typography>

        {/* Current Issue PDF Box */}
        <Box sx={{ mb: 6 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 600,
              color: '#1e293b',
              mb: 3,
              textAlign: 'left'
            }}
          >
            Latest Publication
          </Typography>

          <Card
            elevation={3}
            sx={{
              maxWidth: 400,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: '0 8px 25px rgba(234, 119, 23, 0.15)',
              },
              cursor: 'pointer'
            }}
            onClick={() => window.open(currentIssuePdf, '_blank')}
          >
            <CardContent sx={{ p: 3, textAlign: 'left' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PictureAsPdf sx={{ fontSize: 40, color: '#EA7717', mr: 2 }} />
                <Box>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      textAlign: 'left'
                    }}
                  >
                    Volume 12, Issue 1
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: '#64748b',
                      textAlign: 'left'
                    }}
                  >
                    2025
                  </Typography>
                </Box>
              </Box>

              <Typography
                variant="body1"
                sx={{
                  mb: 3,
                  color: '#475569',
                  textAlign: 'left'
                }}
              >
                Latest research articles and scholarly contributions in professional studies.
              </Typography>

              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  startIcon={<Visibility />}
                  sx={{
                    bgcolor: '#EA7717',
                    '&:hover': { bgcolor: '#d66a14' },
                    textTransform: 'none'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(currentIssuePdf, '_blank');
                  }}
                >
                  View PDF
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Download />}
                  sx={{
                    borderColor: '#EA7717',
                    color: '#EA7717',
                    '&:hover': {
                      backgroundColor: '#EA7717',
                      color: 'white',
                    },
                    textTransform: 'none'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    const link = document.createElement('a');
                    link.href = currentIssuePdf;
                    link.download = 'Volume-12-Issue-1-2025.pdf';
                    link.click();
                  }}
                >
                  Download
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Box>

        {/* Additional Information */}
        <Box sx={{ mt: 4 }}>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: '#1e293b',
              mb: 2,
              textAlign: 'left'
            }}
          >
            About Current Issue
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: '#475569',
              lineHeight: 1.7,
              textAlign: 'left'
            }}
          >
            This issue contains peer-reviewed research articles covering various domains of professional studies including business management, technology, education, and social sciences. All articles have undergone rigorous peer review process to ensure quality and academic standards.
          </Typography>
        </Box>
      </Container>
    </>
  );
};

export default CurrentIssuse;
