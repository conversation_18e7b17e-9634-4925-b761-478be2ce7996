const PeerReviewPolicy = () => {
  return (
    <div className="max-w-4xl mx-auto p-8 bg-white shadow-lg rounded-2xl leading-relaxed">
      {/* Page Title */}
      <h1 className="text-3xl font-bold text-gray-800 mb-6 text-center">
        Peer Review Policy
      </h1>

      {/* Introduction */}
      <p className="text-gray-700 text-justify mb-6">
        The journal’s policy is to have manuscripts reviewed by three expert
        reviewers. Lingaya’s Lalita Devi Journal of Professional Studies
        (LLDJPS) utilizes a{" "}
        <strong>double-blind peer review process</strong> in which the reviewer
        and author's names and information are withheld from the other. All
        manuscripts are reviewed as rapidly as possible while maintaining rigor.
        Reviewers provide comments to the author and recommendations to the
        Editor, who then makes the final decision.
      </p>

      {/* Reviewer Suggestions */}
      <h2 className="text-xl font-semibold text-gray-800 mb-4">
        Reviewer Suggestions
      </h2>
      <p className="text-gray-700 text-justify mb-6">
        As part of the submission process, authors may be asked to provide the
        names of peers who could review the manuscript. Recommended reviewers
        should be experts in the relevant field and able to provide an objective
        assessment of the manuscript. Authors must avoid conflicts of interest
        when suggesting reviewers. Examples of conflicts of interest include:
      </p>

      {/* Conflict of Interest List */}
      <ul className="list-disc list-inside text-gray-700 mb-6 pl-4">
        <li>The reviewer should have no prior knowledge of your submission.</li>
        <li>The reviewer should not have recently collaborated with any author.</li>
        <li>
          Reviewer nominees from the same institution as any author are not
          permitted.
        </li>
      </ul>

      <p className="text-gray-700 text-justify mb-6 font-semibold">
        Please note that Editors are not obliged to invite or exclude any
        recommended or opposed reviewers when assessing your manuscript.
      </p>

      {/* Editorial Submissions */}
      <h2 className="text-xl font-semibold text-gray-800 mb-4">
        Editorial Submissions
      </h2>
      <p className="text-gray-700 text-justify mb-6">
        The Editor or members of the Editorial Board may occasionally submit
        their manuscripts for possible publication. In these cases, the peer
        review process will be managed by alternate Editorial Board members, and
        the submitting Editor or Board member will have no involvement in the
        decision-making process.
      </p>

      {/* Commitment */}
      <h2 className="text-xl font-semibold text-gray-800 mb-4">
        Commitment to Quality
      </h2>
      <p className="text-gray-700 text-justify">
        LLDJPS is committed to delivering a rigorous, high-quality peer review
        process for all submissions. The Editor-in-Chief will also issue a
        letter of appreciation for reviewers who provide detailed and
        constructive feedback to maintain the journal’s standards.
      </p>
    </div>
  );
};

export default PeerReviewPolicy;
