import React from 'react';
import Footer from '../../Components/Footer';
import {
    Container,
    Typography,
    Box,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Grid,
    Card,
    CardContent,
    Chip,
    Alert,
    ThemeProvider,
    createTheme
} from '@mui/material';
import {
    Description,
    FormatListNumbered,
    Assignment,
    Timer,
    CheckCircle,
    School,
    Gavel,
    CloudUpload
} from '@mui/icons-material';

// Custom theme for responsive design
const theme = createTheme({
    palette: {
        primary: {
            main: '#EA7717', // orange-600
            light: '#ff8a4c', // orange-400
            dark: '#b91c1c', // orange-700
        },
        secondary: {
            main: '#feecdc', // orange-200
        },
    },
    typography: {
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
        h1: {
            fontWeight: 700,
            fontSize: '2.5rem',
            '@media (max-width:600px)': {
                fontSize: '2rem',
            },
        },
        h2: {
            fontWeight: 600,
            fontSize: '2rem',
            '@media (max-width:600px)': {
                fontSize: '1.75rem',
            },
        },
        h5: {
            fontWeight: 600,
            fontSize: '1.25rem',
            '@media (max-width:600px)': {
                fontSize: '1.1rem',
            },
        },
    },
    breakpoints: {
        values: {
            xs: 0,
            sm: 600,
            md: 900,
            lg: 1200,
            xl: 1536,
        },
    },
});

function InstructionsForScholars() {
    return (
        <>
            <ThemeProvider theme={theme}>
                <div className="min-h-screen bg-orange-50">
                    <Container maxWidth="lg" sx={{ py: { xs: 4, md: 8 }, px: { xs: 2, sm: 3 } }}>
                        <Typography
                            variant="h1"
                            component="h1"
                            align="center"
                            gutterBottom
                            sx={{
                                color: 'primary.main',
                                fontWeight: 'bold',
                                mb: { xs: 4, md: 6 },
                                px: { xs: 1, sm: 0 }
                            }}
                        >
                            Instructions for Scholars
                        </Typography>
                        {/* Quick Overview Alert */}
                        <Alert
                            severity="info"
                            sx={{
                                mb: { xs: 3, md: 4 },
                                borderRadius: 2,
                                fontSize: { xs: '0.875rem', sm: '1rem' }
                            }}
                        >
                            <Typography variant="body1" sx={{ fontWeight: 500 }}>
                                Welcome to our submission guidelines. Please read all instructions carefully before submitting your manuscript.
                            </Typography>
                        </Alert>

                        {/* Main Content Grid */}
                        <Grid container spacing={{ xs: 2, md: 4 }}>
                            {/* Manuscript Guidelines */}
                            <Grid item xs={12} lg={6}>
                                <Card
                                    elevation={3}
                                    sx={{
                                        height: '100%',
                                        borderRadius: 3,
                                        transition: 'transform 0.2s ease-in-out',
                                        '&:hover': {
                                            transform: 'translateY(-4px)',
                                        }
                                    }}
                                >
                                    <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                            <School color="primary" sx={{ mr: 2, fontSize: { xs: '1.5rem', md: '2rem' } }} />
                                            <Typography
                                                variant="h5"
                                                color="primary"
                                                fontWeight="bold"
                                                sx={{ fontSize: { xs: '1.1rem', md: '1.25rem' } }}
                                            >
                                                Manuscript Guidelines
                                            </Typography>
                                        </Box>

                                        <List sx={{ p: 0 }}>
                                            <ListItem sx={{ px: 0, py: { xs: 1, md: 2 } }}>
                                                <ListItemIcon sx={{ minWidth: { xs: 40, md: 56 } }}>
                                                    <Description color="primary" />
                                                </ListItemIcon>
                                                <ListItemText
                                                    primary={
                                                        <Typography
                                                            variant="subtitle1"
                                                            fontWeight="bold"
                                                            sx={{ fontSize: { xs: '0.9rem', md: '1rem' } }}
                                                        >
                                                            Format Requirements
                                                        </Typography>
                                                    }
                                                    secondary={
                                                        <Typography
                                                            variant="body2"
                                                            color="text.secondary"
                                                            sx={{
                                                                mt: 1,
                                                                fontSize: { xs: '0.8rem', md: '0.875rem' },
                                                                lineHeight: 1.5
                                                            }}
                                                        >
                                                            Manuscripts should be submitted in MS Word format, double-spaced,
                                                            with 12-point Times New Roman font. Maximum length: 8,000 words including footnotes.
                                                        </Typography>
                                                    }
                                                />
                                            </ListItem>

                                            <ListItem sx={{ px: 0, py: { xs: 1, md: 2 } }}>
                                                <ListItemIcon sx={{ minWidth: { xs: 40, md: 56 } }}>
                                                    <FormatListNumbered color="primary" />
                                                </ListItemIcon>
                                                <ListItemText
                                                    primary={
                                                        <Typography
                                                            variant="subtitle1"
                                                            fontWeight="bold"
                                                            sx={{ fontSize: { xs: '0.9rem', md: '1rem' } }}
                                                        >
                                                            Citation Style
                                                        </Typography>
                                                    }
                                                    secondary={
                                                        <Typography
                                                            variant="body2"
                                                            color="text.secondary"
                                                            sx={{
                                                                mt: 1,
                                                                fontSize: { xs: '0.8rem', md: '0.875rem' },
                                                                lineHeight: 1.5
                                                            }}
                                                        >
                                                            Follow the latest edition of The Bluebook: A Uniform System of Citation
                                                            for all references and citations.
                                                        </Typography>
                                                    }
                                                />
                                            </ListItem>
                                        </List>
                                    </CardContent>
                                </Card>
                            </Grid>

                            {/* Submission Process */}
                            <Grid item xs={12} lg={6}>
                                <Card
                                    elevation={3}
                                    sx={{
                                        height: '100%',
                                        borderRadius: 3,
                                        transition: 'transform 0.2s ease-in-out',
                                        '&:hover': {
                                            transform: 'translateY(-4px)',
                                        }
                                    }}
                                >
                                    <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                            <CloudUpload color="primary" sx={{ mr: 2, fontSize: { xs: '1.5rem', md: '2rem' } }} />
                                            <Typography
                                                variant="h5"
                                                color="primary"
                                                fontWeight="bold"
                                                sx={{ fontSize: { xs: '1.1rem', md: '1.25rem' } }}
                                            >
                                                Submission Process
                                            </Typography>
                                        </Box>

                                        <List sx={{ p: 0 }}>
                                            <ListItem sx={{ px: 0, py: { xs: 1, md: 2 } }}>
                                                <ListItemIcon sx={{ minWidth: { xs: 40, md: 56 } }}>
                                                    <Assignment color="primary" />
                                                </ListItemIcon>
                                                <ListItemText
                                                    primary={
                                                        <Typography
                                                            variant="subtitle1"
                                                            fontWeight="bold"
                                                            sx={{ fontSize: { xs: '0.9rem', md: '1rem' } }}
                                                        >
                                                            Required Documents
                                                        </Typography>
                                                    }
                                                    secondary={
                                                        <Box sx={{ mt: 1 }}>
                                                            <Typography
                                                                variant="body2"
                                                                color="text.secondary"
                                                                sx={{
                                                                    fontSize: { xs: '0.8rem', md: '0.875rem' },
                                                                    lineHeight: 1.5,
                                                                    mb: 1
                                                                }}
                                                            >
                                                                Required submission documents:
                                                            </Typography>
                                                            <Box component="ul" sx={{ pl: 2, m: 0 }}>
                                                                {[
                                                                    'Main manuscript (without author details)',
                                                                    'Title page with author information',
                                                                    'Abstract (250 words)',
                                                                    'Keywords (5-7)'
                                                                ].map((item, index) => (
                                                                    <Typography
                                                                        key={index}
                                                                        component="li"
                                                                        variant="body2"
                                                                        color="text.secondary"
                                                                        sx={{
                                                                            fontSize: { xs: '0.8rem', md: '0.875rem' },
                                                                            mb: 0.5
                                                                        }}
                                                                    >
                                                                        {item}
                                                                    </Typography>
                                                                ))}
                                                            </Box>
                                                        </Box>
                                                    }
                                                />
                                            </ListItem>

                                            <ListItem sx={{ px: 0, py: { xs: 1, md: 2 } }}>
                                                <ListItemIcon sx={{ minWidth: { xs: 40, md: 56 } }}>
                                                    <Timer color="primary" />
                                                </ListItemIcon>
                                                <ListItemText
                                                    primary={
                                                        <Typography
                                                            variant="subtitle1"
                                                            fontWeight="bold"
                                                            sx={{ fontSize: { xs: '0.9rem', md: '1rem' } }}
                                                        >
                                                            Review Timeline
                                                        </Typography>
                                                    }
                                                    secondary={
                                                        <Box sx={{ mt: 1 }}>
                                                            {[
                                                                { phase: 'Initial review', time: '2-3 weeks' },
                                                                { phase: 'Peer review', time: '4-6 weeks' },
                                                                { phase: 'Final decision', time: '2 weeks after peer review' }
                                                            ].map((item, index) => (
                                                                <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                                                                    <Typography
                                                                        variant="body2"
                                                                        color="text.secondary"
                                                                        sx={{ fontSize: { xs: '0.8rem', md: '0.875rem' } }}
                                                                    >
                                                                        {item.phase}:
                                                                    </Typography>
                                                                    <Chip
                                                                        label={item.time}
                                                                        size="small"
                                                                        color="primary"
                                                                        variant="outlined"
                                                                        sx={{
                                                                            fontSize: { xs: '0.7rem', md: '0.75rem' },
                                                                            height: { xs: 20, md: 24 }
                                                                        }}
                                                                    />
                                                                </Box>
                                                            ))}
                                                        </Box>
                                                    }
                                                />
                                            </ListItem>
                                        </List>
                                    </CardContent>
                                </Card>
                            </Grid>

                            {/* Publication Ethics - Full Width */}
                            <Grid item xs={12}>
                                <Card
                                    elevation={3}
                                    sx={{
                                        borderRadius: 3,
                                        transition: 'transform 0.2s ease-in-out',
                                        '&:hover': {
                                            transform: 'translateY(-4px)',
                                        }
                                    }}
                                >
                                    <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                            <Gavel color="primary" sx={{ mr: 2, fontSize: { xs: '1.5rem', md: '2rem' } }} />
                                            <Typography
                                                variant="h5"
                                                color="primary"
                                                fontWeight="bold"
                                                sx={{ fontSize: { xs: '1.1rem', md: '1.25rem' } }}
                                            >
                                                Publication Ethics
                                            </Typography>
                                        </Box>

                                        <List sx={{ p: 0 }}>
                                            <ListItem sx={{ px: 0, py: { xs: 1, md: 2 } }}>
                                                <ListItemIcon sx={{ minWidth: { xs: 40, md: 56 } }}>
                                                    <CheckCircle color="primary" />
                                                </ListItemIcon>
                                                <ListItemText
                                                    primary={
                                                        <Typography
                                                            variant="subtitle1"
                                                            fontWeight="bold"
                                                            sx={{ fontSize: { xs: '0.9rem', md: '1rem' } }}
                                                        >
                                                            Ethical Guidelines
                                                        </Typography>
                                                    }
                                                    secondary={
                                                        <Typography
                                                            variant="body2"
                                                            color="text.secondary"
                                                            sx={{
                                                                mt: 1,
                                                                fontSize: { xs: '0.8rem', md: '0.875rem' },
                                                                lineHeight: 1.5
                                                            }}
                                                        >
                                                            Authors must ensure original work, proper attribution of sources,
                                                            and disclosure of any conflicts of interest. Plagiarism and duplicate
                                                            submissions are strictly prohibited. All submissions undergo rigorous
                                                            plagiarism detection and ethical review.
                                                        </Typography>
                                                    }
                                                />
                                            </ListItem>
                                        </List>
                                    </CardContent>
                                </Card>
                            </Grid>
                        </Grid>
                    </Container>
                </div>
            </ThemeProvider>
            <Footer />
        </>
    );
}

export default InstructionsForScholars;