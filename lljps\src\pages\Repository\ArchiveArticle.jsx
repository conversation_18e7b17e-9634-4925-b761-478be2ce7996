import React, { useState } from 'react';
import { Container, Typography, <PERSON>, Card, CardContent, Button, Grid } from '@mui/material';
import { PictureAsPdf, Download, Visibility, CalendarToday } from '@mui/icons-material';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';

// Import PDFs
import volume10Issue1 from '../../assets/Archive Article/Volume 10, Issue1, June 2023.pdf';
import volume10Issue2 from '../../assets/Archive Article/Volume 10, Issue 2- 2023.pdf';
import volume11Issue1 from '../../assets/Archive Article/Volume 11, Issue 1, 2024.pdf';
import volume11Issue2 from '../../assets/Archive Article/Volume 11, Issue 2, 2024.pdf';
import volume9Issue1 from '../../assets/Archive Article/Volume 9, Issue 1, 2022.pdf';

const ArchiveArticle = () => {
  const archiveIssues = [
    {
      title: 'Volume 11, Issue 2',
      year: '2024',
      description: 'Research articles covering various domains of professional studies',
      pdf: volume11Issue2,
      fileName: 'Volume-11-Issue-2-2024.pdf'
    },
    {
      title: 'Volume 11, Issue 1',
      year: '2024',
      description: 'Peer-reviewed research in business, technology, and social sciences',
      pdf: volume11Issue1,
      fileName: 'Volume-11-Issue-1-2024.pdf'
    },
    {
      title: 'Volume 10, Issue 2',
      year: '2023',
      description: 'Scholarly contributions in professional studies and research',
      pdf: volume10Issue2,
      fileName: 'Volume-10-Issue-2-2023.pdf'
    },
    {
      title: 'Volume 10, Issue 1',
      year: '2023',
      description: 'June 2023 edition with multidisciplinary research articles',
      pdf: volume10Issue1,
      fileName: 'Volume-10-Issue-1-June-2023.pdf'
    },
    {
      title: 'Volume 9, Issue 1',
      year: '2022',
      description: 'Research articles from 2022 covering diverse academic fields',
      pdf: volume9Issue1,
      fileName: 'Volume-9-Issue-1-2022.pdf'
    }
  ];

  return (
    <>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography
          variant="h3"
          component="h1"
          sx={{
            fontWeight: 700,
            color: '#EA7717',
            mb: 4,
            textAlign: 'left'
          }}
        >
          Archive Articles
        </Typography>

        <Typography
          variant="h6"
          sx={{
            color: '#64748b',
            mb: 6,
            textAlign: 'left'
          }}
        >
          Browse our collection of published research articles from previous issues
        </Typography>

        <Grid container spacing={4}>
          {archiveIssues.map((issue, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card
                elevation={3}
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 25px rgba(234, 119, 23, 0.15)',
                  },
                  cursor: 'pointer'
                }}
                onClick={() => window.open(issue.pdf, '_blank')}
              >
                <CardContent sx={{ p: 3, flexGrow: 1, textAlign: 'left' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PictureAsPdf sx={{ fontSize: 40, color: '#EA7717', mr: 2 }} />
                    <Box>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          textAlign: 'left'
                        }}
                      >
                        {issue.title}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        <CalendarToday sx={{ fontSize: 16, color: '#64748b', mr: 1 }} />
                        <Typography
                          variant="body2"
                          sx={{
                            color: '#64748b',
                            textAlign: 'left'
                          }}
                        >
                          {issue.year}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Typography
                    variant="body2"
                    sx={{
                      mb: 3,
                      color: '#475569',
                      textAlign: 'left',
                      flexGrow: 1
                    }}
                  >
                    {issue.description}
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 'auto' }}>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<Visibility />}
                      sx={{
                        bgcolor: '#EA7717',
                        '&:hover': { bgcolor: '#d66a14' },
                        textTransform: 'none'
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(issue.pdf, '_blank');
                      }}
                    >
                      View
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<Download />}
                      sx={{
                        borderColor: '#EA7717',
                        color: '#EA7717',
                        '&:hover': {
                          backgroundColor: '#EA7717',
                          color: 'white',
                        },
                        textTransform: 'none'
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        const link = document.createElement('a');
                        link.href = issue.pdf;
                        link.download = issue.fileName;
                        link.click();
                      }}
                    >
                      Download
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </>
  );
};

export default ArchiveArticle;
