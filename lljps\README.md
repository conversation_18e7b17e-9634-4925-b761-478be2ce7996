# LLDJPS - Journal Website

A modern React-based journal website built with Vite, Material-UI, and Tailwind CSS.

## 🚀 Deployment

This project is optimized for Vercel deployment with the following features:
- ✅ React SPA with client-side routing
- ✅ Optimized build configuration
- ✅ Proper asset caching headers
- ✅ SPA fallback routing for React Router

## 📦 Technologies Used

- **React 19** - Modern React with latest features
- **Vite** - Fast build tool and dev server
- **Material-UI (MUI)** - Component library
- **Tailwind CSS** - Utility-first CSS framework
- **React Router DOM** - Client-side routing
- **Lucide React** - Icon library

## 🛠️ Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🌐 Vercel Deployment

1. Connect your GitHub repository to Vercel
2. Vercel will automatically detect this as a Vite project
3. Build settings are pre-configured in `vercel.json`
4. Deploy with one click!

### Build Configuration
- **Framework**: Vite
- **Build Command**: `npm run build`
- **Output Directory**: `dist`
- **Install Command**: `npm install`

## 📁 Project Structure

```
src/
├── Components/          # Reusable components
├── pages/              # Page components
│   ├── AboutUs/        # About section pages
│   ├── EditorialOraganization/  # Editorial pages
│   ├── Repository/     # Repository pages
│   └── policies/       # Policy pages
├── theme/              # MUI theme configuration
├── hooks/              # Custom React hooks
├── constants/          # App constants
└── assets/             # Static assets
```

## 🎨 Theming

The project uses a custom MUI theme with:
- Primary color: `#EA7717` (Orange)
- Responsive typography
- Custom component overrides
- Dark/light mode support

## 📱 Features

- **Responsive Design** - Works on all devices
- **Modern UI** - Clean, professional interface
- **Fast Performance** - Optimized with Vite
- **SEO Ready** - Proper meta tags and structure
- **Accessible** - WCAG compliant components
